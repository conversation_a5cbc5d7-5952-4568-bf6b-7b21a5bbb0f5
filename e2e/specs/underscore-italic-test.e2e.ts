import { chromium } from 'playwright';
import type { <PERSON><PERSON><PERSON>, <PERSON> } from 'playwright';
import { expect } from 'vitest';

import { verifyPluginAvailable, waitForAsyncOperation } from '../helpers/plugin-setup';
import { registerPageForUIReset } from '../helpers/test-setup';

/**
 * E2E Test for Underscore Italic Formatting Issue
 *
 * This test specifically verifies that underscore italic formatting (_text_)
 * works correctly during conversion from Obsidian to Lexical format.
 */

describe("Underscore Italic Formatting E2E Test", () => {
  let browser: Browser;
  let page: Page;
  const testFilePath = 'articles/underscore-italic-test.md';

  beforeAll(async () => {
    // Connect to existing Obsidian instance via CDP
    browser = await chromium.connectOverCDP('http://127.0.0.1:9222');

    // Get the first page (Obsidian window)
    const contexts = browser.contexts();
    const context = contexts[0];
    const pages = context.pages();
    page = pages[0];

    console.log("Connected to Obs<PERSON> via Playwright");

    // Enable the ghost-sync plugin
    await page.evaluate(() => {
      (window as any).app.plugins.setEnable(true);
      (window as any).app.plugins.enablePlugin('ghost-sync');
    });

    await waitForAsyncOperation(500);
    await verifyPluginAvailable(page);
    registerPageForUIReset(page);
  });

  beforeEach(async () => {
    // Clean up any open dialogs or modals before each test
    await page.evaluate(() => {
      const modals = document.querySelectorAll('.modal, .modal-container, .publish-dialog, [data-modal]');
      modals.forEach(modal => {
        const closeButton = modal.querySelector('.modal-close, .close, [aria-label="Close"], button[data-action="close"]');
        if (closeButton) {
          (closeButton as HTMLElement).click();
        } else {
          modal.remove();
        }
      });

      const notices = document.querySelectorAll('.notice');
      notices.forEach(notice => notice.remove());

      document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape', bubbles: true }));
    });

    await waitForAsyncOperation(500);
  });

  afterEach(async () => {
    // Clean up test file
    await page.evaluate(async ({ filePath }) => {
      const app = (window as any).app;
      try {
        const file = app.vault.getAbstractFileByPath(filePath);
        if (file) {
          await app.vault.delete(file);
        }
      } catch (error) {
        console.log('Error cleaning up file:', filePath, error);
      }
    }, { filePath: testFilePath });

    await waitForAsyncOperation(1000);
  });

  afterAll(async () => {
    if (browser) {
      await browser.close();
    }
  });

  test("should correctly convert underscore italic formatting to Lexical", async () => {
    console.log("Testing underscore italic formatting conversion");

    // Create test content with various italic formatting patterns
    const testContent = `---
title: "Underscore Italic Test"
slug: "underscore-italic-test"
status: draft
---

# Underscore Italic Formatting Test

This paragraph contains _underscore italic text_ that should be converted to Lexical format.

Here are multiple examples:
- _Simple italic text_
- This has _italic words_ in the middle
- _Multiple_ _italic_ _sections_
- Mixed with *star italic* and _underscore italic_

## More Complex Examples

_This is a longer italic sentence that spans multiple words and should be properly converted._

Regular text with _italic_ and **bold** formatting.

> A blockquote with _italic text_ inside.

- List item with _italic text_
- Another item with _more italic text_

\`\`\`javascript
// Code block (should not affect italic parsing)
const text = "_this should not be italic_";
\`\`\`

Final paragraph with _final italic text_.`;

    // Create test file
    await page.evaluate(async ({ path, content }) => {
      const app = (window as any).app;

      // Ensure articles directory exists
      const articlesDir = 'articles';
      const abstractFile = app.vault.getAbstractFileByPath(articlesDir);
      if (!abstractFile) {
        await app.vault.createFolder(articlesDir);
      }

      await app.vault.create(path, content);
    }, { path: testFilePath, content: testContent });

    await waitForAsyncOperation(1000);

    // Test conversion from Obsidian markdown to Lexical format
    const conversionResult = await page.evaluate(async ({ filePath }) => {
      const app = (window as any).app;
      const plugin = app.plugins.plugins['ghost-sync'];
      const file = app.vault.getAbstractFileByPath(filePath);

      if (!file || !plugin || !plugin.lexicalParser) {
        return { success: false, error: 'Required components not available' };
      }

      const content = await app.vault.read(file);

      // Extract just the content part (without frontmatter)
      const contentWithoutFrontmatter = content.replace(/^---[\s\S]*?---\n/, '');

      // Count underscore italic patterns in original content
      const originalUnderscoreItalics = (contentWithoutFrontmatter.match(/_[^_\n]+_/g) || []);

      try {
        // Convert to Lexical format
        const lexicalResult = await plugin.lexicalParser.markdownToLexical(contentWithoutFrontmatter);

        if (!lexicalResult.success) {
          return {
            success: false,
            error: 'Failed to convert to Lexical',
            details: lexicalResult.error,
            originalUnderscoreCount: originalUnderscoreItalics.length
          };
        }

        // Analyze the Lexical AST for italic formatting
        const italicNodes: Array<{
          text: string;
          format: number;
          style?: string;
          path: string;
        }> = [];

        function findItalicNodes(node: any, path: string = 'root') {
          if (node.type === 'text' && node.format && (node.format & 2) === 2) {
            italicNodes.push({
              text: node.text,
              format: node.format,
              style: node.style,
              path: path
            });
          }

          if (node.children) {
            node.children.forEach((child: any, index: number) => {
              findItalicNodes(child, `${path}.children[${index}]`);
            });
          }
        }

        findItalicNodes(lexicalResult.data.root);

        // Test round-trip conversion
        const markdownResult = await plugin.lexicalParser.lexicalToMarkdown(lexicalResult.data);
        let roundTripSuccess = false;
        let convertedUnderscoreCount = 0;

        if (markdownResult.success) {
          const convertedUnderscoreItalics = (markdownResult.data.match(/_[^_\n]+_/g) || []);
          convertedUnderscoreCount = convertedUnderscoreItalics.length;
          roundTripSuccess = true;
        }

        return {
          success: true,
          originalUnderscoreCount: originalUnderscoreItalics.length,
          originalUnderscoreTexts: originalUnderscoreItalics,
          lexicalItalicCount: italicNodes.length,
          italicNodes: italicNodes,
          roundTripSuccess: roundTripSuccess,
          convertedUnderscoreCount: convertedUnderscoreCount,
          formattingPreserved: originalUnderscoreItalics.length === convertedUnderscoreCount
        };
      } catch (error) {
        return {
          success: false,
          error: error.message,
          originalUnderscoreCount: originalUnderscoreItalics.length
        };
      }
    }, { filePath: testFilePath });

    // Log detailed results
    console.log(`Original underscore italic patterns: ${conversionResult.originalUnderscoreCount}`);
    if (conversionResult.originalUnderscoreTexts) {
      console.log('Original patterns:', conversionResult.originalUnderscoreTexts);
    }
    console.log(`Lexical italic nodes found: ${conversionResult.lexicalItalicCount}`);
    console.log(`Round-trip underscore italic patterns: ${conversionResult.convertedUnderscoreCount}`);
    console.log(`Formatting preserved: ${conversionResult.formattingPreserved}`);

    // Assertions
    expect(conversionResult.success).toBe(true);
    expect(conversionResult.originalUnderscoreCount).toBeGreaterThan(0);
    expect(conversionResult.lexicalItalicCount).toBeGreaterThan(0);

    // The key test: underscore italic should convert to Lexical italic format
    expect(conversionResult.lexicalItalicCount).toBeGreaterThanOrEqual(conversionResult.originalUnderscoreCount);

    // Round-trip should preserve formatting
    expect(conversionResult.roundTripSuccess).toBe(true);
    expect(conversionResult.formattingPreserved).toBe(true);

    // Log details about italic nodes for debugging
    if (conversionResult.italicNodes) {
      conversionResult.italicNodes.forEach((node: any, index: number) => {
        console.log(`Italic node ${index + 1}: "${node.text}" (format: ${node.format}, style: ${node.style || 'none'})`);
      });
    }
  });
});
